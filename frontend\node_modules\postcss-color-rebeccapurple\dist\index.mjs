import e from"postcss-value-parser";const r=r=>{const s=Object.assign({preserve:!1},r);return{postcssPlugin:"postcss-color-rebeccapurple",Declaration(r){if(!r.value.toLowerCase().includes("rebeccapurple"))return;const o=e(r.value);o.walk((e=>{"word"===e.type&&"rebeccapurple"===e.value.toLowerCase()&&(e.value="#639")}));const a=String(o);a!==r.value&&(r.cloneBefore({value:a}),s.preserve||r.remove())}}};r.postcss=!0;export{r as default};
